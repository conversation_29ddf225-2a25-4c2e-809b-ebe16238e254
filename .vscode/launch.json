{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Current File", "skipFiles": ["<node_internals>/**"], "program": "${file}", "preLaunchTask": "tsc: build - functions", "outFiles": ["${workspaceFolder}/backend/functions/lib/**/*.js"]}, {"type": "node", "request": "launch", "name": "Debug Live File", "program": "${file}", "runtimeExecutable": "${workspaceFolder}/backend/functions/node_modules/.bin/ts-node", "args": [], "cwd": "${workspaceFolder}/backend/functions", "env": {"TS_NODE_PROJECT": "${workspaceFolder}/backend/functions/tsconfig.json"}, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "nodeVersionHint": 22.15, "request": "launch", "runtimeVersion": "22.15.0", "runtimeExecutable": "npm", "name": "Start Frontend", "runtimeArgs": ["run", "start"], "cwd": "${workspaceFolder}/frontend/desktop"}]}
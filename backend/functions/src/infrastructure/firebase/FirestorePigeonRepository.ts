import * as admin from "firebase-admin";
import { PigeonRepository } from "../../domain/repositories/PigeonRepository";
import { PigeonDocument } from "../../domain/entities/Pigeon";

export class FirestorePigeonRepository implements PigeonRepository {
    private collection = admin.firestore().collection("pigeons");

    async save(pigeon: PigeonDocument): Promise<void> {
        await this.collection.doc(pigeon.id).set(pigeon);
    }

    async getByOwnerId(ownerId: string, limit?: number, offset?: number): Promise<PigeonDocument[]> {
        const pigeons: PigeonDocument[] = [];
        let query = this.collection.where("ownerId", "==", ownerId).orderBy("capturedAt", "desc");

        if (offset && offset > 0) {
            query = query.offset(offset);
        }

        if (limit && limit > 0) {
            query = query.limit(limit);
        }

        const snapshot = await query.get();

        snapshot.forEach((doc) => {
            const data = doc.data();
            const pigeon = {
                ...data,
                capturedAt:
                    data.capturedAt instanceof admin.firestore.Timestamp ? data.capturedAt.toDate() : data.capturedAt,
            } as PigeonDocument;

            pigeons.push(pigeon);
        });

        return pigeons;
    }

    async getByCaptureId(captureId: string): Promise<PigeonDocument | null> {
        const doc = await this.collection.where("captureId", "==", captureId).get();
        if (!doc.docs.length) {
            return null;
        }
        return doc.docs[0].data() as PigeonDocument;
    }
}

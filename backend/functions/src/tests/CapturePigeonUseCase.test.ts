/* eslint-disable @typescript-eslint/no-unused-vars */
import { AnalysisJob } from "../domain/entities/AnalysisJob";
import { Pigeon } from "../domain/entities/Pigeon";
import { defaultTrainer, Trainer, TrainerDocument } from "../domain/entities/Trainer";
import { TrainerStats } from "../domain/entities/TrainerStats";
import { AnalysisJobErrorCode } from "../domain/enums/AnalysisJobErrorCode";
import { AnalysisJobStatus } from "../domain/enums/AnalysisJobStatus";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { TrainerStatsOrderBy } from "../domain/enums/TrainerStatsOrderBy";
import { AnalysisJobRepository } from "../domain/repositories/AnalysisJobRepository";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { TrainerStatsRepository } from "../domain/repositories/TrainerStatsRepository";
import { AnalysisService, RankedBasePigeon } from "../domain/services/AnalysisService";
import { Coordinates, GeoLocationService } from "../domain/services/GeoLocationService";
import { StatsService } from "../domain/services/StatsService";
import { defaultBasePigeon } from "../domain/value-objects/BasePigeon";
import { CapturePigeonUseCase } from "../use-cases/CapturePigeonUseCase";
import { PingExternalServiceUseCase } from "../use-cases/PingExternalServiceUseCase";

class FakeTrainerRepository implements TrainerRepository {
    private trainers: { [id: string]: Trainer } = {};

    async getById(id: string): Promise<Trainer | null> {
        return this.trainers[id] || null;
    }

    async getByIds(ids: string[]): Promise<Trainer[]> {
        return ids.map((id) => this.trainers[id]).filter(Boolean) as Trainer[];
    }

    async update(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer({ ...trainer, decks: [] });
    }

    add(trainer: Trainer) {
        this.trainers[trainer.id] = trainer;
    }
    async create(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer(trainer);
    }
}

class FakePigeonRepository implements PigeonRepository {
    public pigeons: { [id: string]: Pigeon } = {};

    async save(pigeon: Pigeon): Promise<void> {
        this.pigeons[pigeon.id] = pigeon;
    }

    async getByOwnerId(ownerId: string, limit?: number, offset?: number): Promise<Pigeon[]> {
        let pigeons = Object.values(this.pigeons).filter((pigeon) => pigeon.ownerId === ownerId);

        // Sort by capturedAt desc to match the real implementation
        pigeons = pigeons.sort((a, b) => b.capturedAt.getTime() - a.capturedAt.getTime());

        if (offset && offset > 0) {
            pigeons = pigeons.slice(offset);
        }

        if (limit && limit > 0) {
            pigeons = pigeons.slice(0, limit);
        }

        return pigeons;
    }

    async getByCaptureId(captureId: string): Promise<Pigeon | null> {
        const pigeon = Object.values(this.pigeons).find((p) => p.captureId === captureId);
        return pigeon || null;
    }
}

class FakeAnalysisService implements AnalysisService {
    analyzePigeonPicture(storageFilePath: string): Promise<RankedBasePigeon[]> {
        // Return a mock ranked base pigeon for testing
        const mockBasePigeon = defaultBasePigeon;

        return Promise.resolve([
            {
                basePigeon: mockBasePigeon,
                score: 85,
            },
        ]);
    }
}

class FakeGeoLocationService implements GeoLocationService {
    determineGang(coordinates: Coordinates, date?: Date): PigeonGang {
        // For testing, use a deterministic approach based on coordinates
        const { latitude, longitude } = coordinates;

        // Use a simple rule for testing
        if (latitude > 0 && longitude > 0) {
            return PigeonGang.MAISON_ROSSINI;
        } else if (latitude > 0 && longitude <= 0) {
            return PigeonGang.LES_CRUMBS;
        } else if (latitude <= 0 && longitude <= 0) {
            return PigeonGang.MAISON_CONTI;
        } else if (latitude <= 0 && longitude > 0) {
            return PigeonGang.LE_SYNDICAT;
        }

        // Default
        return PigeonGang.MAISON_ROSSINI;
    }
}

class FakeAnalysisJobRepository implements AnalysisJobRepository {
    private jobs: Map<string, AnalysisJob> = new Map();

    async create(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async update(job: any): Promise<void> {
        this.jobs.set(job.captureId, AnalysisJob.fromDocument(job));
    }

    async getByCaptureId(captureId: string): Promise<AnalysisJob | null> {
        return this.jobs.get(captureId) || null;
    }

    async getByTrainerId(trainerId: string, limit?: number, offset?: number): Promise<AnalysisJob[]> {
        return Array.from(this.jobs.values()).filter((job) => job.trainerId === trainerId);
    }

    // Helper method for testing
    addJob(job: AnalysisJob): void {
        this.jobs.set(job.captureId, job);
    }

    clear(): void {
        this.jobs.clear();
    }
}

class FakeTrainerStatsRepository implements TrainerStatsRepository {
    private trainerStats: Map<string, TrainerStats> = new Map();

    async getById(id: string): Promise<TrainerStats | null> {
        return this.trainerStats.get(id) || null;
    }

    async create(trainerStats: any): Promise<void> {
        this.trainerStats.set(trainerStats.trainerId, trainerStats);
    }

    async update(trainerStats: any): Promise<void> {
        this.trainerStats.set(trainerStats.trainerId, trainerStats);
    }

    async getByTrainerId(trainerId: string): Promise<TrainerStats | null> {
        return this.trainerStats.get(trainerId) || null;
    }

    async getLeaderBoard(orderBy: TrainerStatsOrderBy, limit: number): Promise<TrainerStats[]> {
        return Array.from(this.trainerStats.values());
    }
}

class FakePingExternalServiceUseCase implements PingExternalServiceUseCase {
    readonly whatsappServerUrl = "https://whatsapp.kiosk.cool";

    async execute(data: Record<string, unknown>): Promise<void> {
        // Do nothing, just return
    }
}

describe("CapturePigeonWithAnalysisUseCase", () => {
    const fakeTrainerRepo = new FakeTrainerRepository();
    const fakePigeonRepo = new FakePigeonRepository();
    const fakeAnalysisJobRepo = new FakeAnalysisJobRepository();
    const analysisService = new FakeAnalysisService();
    const geoLocationService = new FakeGeoLocationService();
    const pingExternalServiceUseCase = new FakePingExternalServiceUseCase();
    const statsService = new StatsService();
    const fakeTrainerStatsRepo = new FakeTrainerStatsRepository();
    const capturePigeonUseCase = new CapturePigeonUseCase(
        fakeTrainerRepo,
        fakePigeonRepo,
        analysisService,
        geoLocationService,
        fakeAnalysisJobRepo,
        pingExternalServiceUseCase,
        statsService,
        fakeTrainerStatsRepo,
    );

    it("should return early if no analysis job found", async () => {
        const filePath = "shots/unknownTrainerId/unknownCaptureId/40.7128_-74.0060_file.jpg";
        // Should not throw, just return early since no analysis job exists
        await capturePigeonUseCase.execute(filePath, filePath);
        // No pigeon should be created
        expect(Object.keys(fakePigeonRepo.pigeons)).toHaveLength(0);
    });

    it("should mark job as error if no pigeon balls", async () => {
        const trainerId = "noBallTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 0 });
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeon: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        await capturePigeonUseCase.execute(filePath, filePath);

        // Check that the job was marked as error
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.ERROR);
        expect(updatedJob?.errorMessage).toBe("No capture stock available");
        expect(updatedJob?.errorCode).toBe(AnalysisJobErrorCode.NO_CAPTURE_STOCK);
    });

    it("should mark job as error with the right error code if analysis failed", async () => {
        const trainerId = "oneBallTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeon: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        // Mock analysis service to throw an error
        const originalAnalyze = analysisService.analyzePigeonPicture;
        analysisService.analyzePigeonPicture = jest.fn().mockRejectedValue(new Error("Analysis failed"));

        await capturePigeonUseCase.execute(filePath, filePath);

        // Restore original method
        analysisService.analyzePigeonPicture = originalAnalyze;

        // Check that the job was marked as error
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.ERROR);
        expect(updatedJob?.errorCode).toBe(AnalysisJobErrorCode.ANALYSIS_FAILED);
    });

    it("should capture a pigeon with invalid coordinates", async () => {
        const trainerId = "oneBallTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        const filePath = `shots/${trainerId}/${captureId}/invalid_coords_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeon: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        await capturePigeonUseCase.execute(filePath, filePath);

        const pigeonList = Object.values(fakePigeonRepo.pigeons);
        const capturedPigeon = pigeonList.find((p) => p.ownerId === trainerId);
        expect(capturedPigeon).toBeDefined();
        expect(capturedPigeon?.ownerId).toBe(trainerId);
        expect(capturedPigeon?.gang).toBe(PigeonGang.MAISON_CONTI); // Default coordinates (0,0) maps to Gang THREE in our test logic
        expect(capturedPigeon?.captureId).toBe(captureId);

        const updatedTrainer = await fakeTrainerRepo.getById(trainerId);
        expect(updatedTrainer?.pigeonBalls).toBe(0);

        const capturedPigeonWithBasePigeon = {
            ...capturedPigeon,
            basePigeon: defaultBasePigeon,
        };

        // Check that the job was marked as finished
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.FINISHED);
        expect(updatedJob?.pigeon).toStrictEqual(capturedPigeonWithBasePigeon);
    });

    it("should capture a pigeon with valid coordinates and call external service on success", async () => {
        const trainerId = "twoBallTrainerId";
        const captureId = "testCaptureId2";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        // Coordinates in the filename (positive lat, negative long - should be Gang TWO in our test)
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeon: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        const spy = jest.spyOn(pingExternalServiceUseCase, "execute");

        await capturePigeonUseCase.execute(filePath, filePath);

        const pigeonList = Object.values(fakePigeonRepo.pigeons);
        const capturedPigeon = pigeonList.find((p) => p.ownerId === trainerId);
        expect(capturedPigeon).toBeDefined();
        expect(capturedPigeon?.ownerId).toBe(trainerId);
        expect(capturedPigeon?.gang).toBe(PigeonGang.LES_CRUMBS); // Based on our test coordinates
        expect(capturedPigeon?.captureId).toBe(captureId);

        const updatedTrainer = await fakeTrainerRepo.getById(trainerId);
        expect(updatedTrainer?.pigeonBalls).toBe(0);

        const capturedPigeonWithBasePigeon = {
            ...capturedPigeon,
            basePigeon: defaultBasePigeon,
        };

        // Check that the job was marked as finished
        const updatedJob = await fakeAnalysisJobRepo.getByCaptureId(captureId);
        expect(updatedJob?.status).toBe(AnalysisJobStatus.FINISHED);
        expect(updatedJob?.pigeon).toStrictEqual(capturedPigeonWithBasePigeon);

        // Check that the external service was called
        expect(spy).toHaveBeenCalledWith({ captureId, trainerId });
    });

    it("should create trainer stats if none", async () => {
        const trainerId = "noStatsTrainerId";
        const captureId = "testCaptureId";
        const trainer = new Trainer({ ...defaultTrainer, id: trainerId, pigeonBalls: 1 });
        const filePath = `shots/${trainerId}/${captureId}/40.7128_-74.0060_file.jpg`;

        // Create analysis job first
        const analysisJob = new AnalysisJob({
            captureId,
            trainerId,
            storageFilePath: filePath,
            status: AnalysisJobStatus.PENDING,
            errorCode: null,
            errorMessage: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            pigeon: null,
        });
        fakeAnalysisJobRepo.addJob(analysisJob);
        fakeTrainerRepo.add(trainer);

        await capturePigeonUseCase.execute(filePath, filePath);

        const trainerStats = await fakeTrainerStatsRepo.getByTrainerId(trainerId);
        expect(trainerStats).toBeDefined();
        expect(trainerStats?.trainerId).toBe(trainerId);
        expect(trainerStats?.pigeonCount).toBe(1);
        expect(trainerStats?.distinctPigeonCount).toBe(1);
        expect(trainerStats?.basePigeonList).toContain(defaultBasePigeon.id);
    });
});

import { PigeonDocument } from "../domain/entities/Pigeon";
import { default<PERSON><PERSON><PERSON>, Trainer, TrainerDocument } from "../domain/entities/Trainer";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { PigeonRarity } from "../domain/enums/PigeonRarity";
import { PigeonRepository } from "../domain/repositories/PigeonRepository";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { BasePigeonRepository } from "../domain/repositories/BasePigeonRepository";
import { defaultStats } from "../domain/value-objects/Stats";
import { BasePigeon, defaultBasePigeon } from "../domain/value-objects/BasePigeon";
import { GetTrainerPigeonsUseCase } from "../use-cases/GetTrainerPigeonsUseCase";

class FakeTrainerRepository implements TrainerRepository {
    private trainers: { [id: string]: Trainer } = {};

    async getById(id: string): Promise<Trainer | null> {
        return this.trainers[id] || null;
    }

    async getByIds(ids: string[]): Promise<Trainer[]> {
        return ids.map((id) => this.trainers[id]).filter(Boolean) as Trainer[];
    }

    async update(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer({ ...trainer, decks: [] });
    }

    add(trainer: Trainer) {
        this.trainers[trainer.id] = trainer;
    }
    async create(trainer: TrainerDocument): Promise<void> {
        this.trainers[trainer.id] = new Trainer(trainer);
    }
}

class FakePigeonRepository implements PigeonRepository {
    public pigeons: { [id: string]: PigeonDocument } = {};

    async save(pigeon: PigeonDocument): Promise<void> {
        this.pigeons[pigeon.id] = pigeon;
    }

    async getByOwnerId(ownerId: string, limit?: number, offset?: number): Promise<PigeonDocument[]> {
        let pigeons = Object.values(this.pigeons).filter((pigeon) => pigeon.ownerId === ownerId);

        // Sort by capturedAt desc to match the real implementation
        pigeons = pigeons.sort((a, b) => b.capturedAt.getTime() - a.capturedAt.getTime());

        if (offset && offset > 0) {
            pigeons = pigeons.slice(offset);
        }

        if (limit && limit > 0) {
            pigeons = pigeons.slice(0, limit);
        }

        return pigeons;
    }

    async getByCaptureId(captureId: string): Promise<PigeonDocument | null> {
        const pigeon = Object.values(this.pigeons).find((p) => p.captureId === captureId);
        return pigeon || null;
    }
}

class FakeBasePigeonRepository implements BasePigeonRepository {
    public basePigeons: { [id: string]: BasePigeon } = {};

    async getAll(): Promise<BasePigeon[]> {
        return Object.values(this.basePigeons);
    }

    async getById(id: string): Promise<BasePigeon | null> {
        return this.basePigeons[id] || null;
    }

    async add(pigeon: BasePigeon): Promise<void> {
        this.basePigeons[pigeon.id] = pigeon;
    }

    async getByIds(ids: string[]): Promise<BasePigeon[]> {
        return ids.map((id) => this.basePigeons[id]).filter(Boolean) as BasePigeon[];
    }

    addBasePigeon(basePigeon: BasePigeon) {
        this.basePigeons[basePigeon.id] = basePigeon;
    }
}

describe("GetTrainerPigeonsUseCase", () => {
    const fakeTrainerRepo = new FakeTrainerRepository();
    const fakePigeonRepo = new FakePigeonRepository();
    const fakeBasePigeonRepo = new FakeBasePigeonRepository();
    const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(fakeTrainerRepo, fakePigeonRepo, fakeBasePigeonRepo);

    beforeEach(() => {
        // Clear repositories before each test
        fakeTrainerRepo.add(new Trainer({ ...defaultTrainer, id: "testTrainerId" }));
        fakePigeonRepo.pigeons = {};
        fakeBasePigeonRepo.basePigeons = {};

        // Add some base pigeons for testing
        fakeBasePigeonRepo.addBasePigeon({
            ...defaultBasePigeon,
            id: "basePigeon1",
            name: { fr: "Pigeon Base 1", en: "Base Pigeon 1" },
        });
        fakeBasePigeonRepo.addBasePigeon({
            ...defaultBasePigeon,
            id: "basePigeon2",
            name: { fr: "Pigeon Base 2", en: "Base Pigeon 2" },
        });
        fakeBasePigeonRepo.addBasePigeon({
            ...defaultBasePigeon,
            id: "basePigeon3",
            name: { fr: "Pigeon Base 3", en: "Base Pigeon 3" },
        });
    });

    it("should throw if trainer not found", async () => {
        await expect(getTrainerPigeonsUseCase.execute("unknownTrainerId")).rejects.toThrow("Trainer not found");
    });

    it("should return empty pigeons array if trainer has no pigeons", async () => {
        const result = await getTrainerPigeonsUseCase.execute("testTrainerId");
        expect(result.pigeons).toEqual([]);
    });

    it("should return all pigeons owned by the trainer", async () => {
        const trainerId = "testTrainerId";
        const testDate = new Date();

        // Add some pigeons for the trainer
        const pigeon1: PigeonDocument = {
            id: "pigeon1",
            ownerId: trainerId,
            rarity: PigeonRarity.COMMON,
            baseStats: defaultStats,
            basePigeonId: "basePigeon1",
            currentFatigue: 5,
            maxFatigue: 5,
            originalPicture: {
                smallThumbnailUrl: "url1",
                originalUrl: "url1",
            },
            items: {
                hat: null,
                mount: null,
                leftWing: null,
                rightWing: null,
            },
            capturedAt: testDate,
            level: 1,
            trickeryPoints: 0,
            auraPoints: 0,
            gang: PigeonGang.MAISON_ROSSINI,
            captureId: "capture1",
        };

        const pigeon2: PigeonDocument = {
            id: "pigeon2",
            ownerId: trainerId,
            rarity: PigeonRarity.RARE,
            baseStats: defaultStats,
            basePigeonId: "basePigeon2",
            currentFatigue: 5,
            maxFatigue: 5,
            originalPicture: {
                smallThumbnailUrl: "url2",
                originalUrl: "url2",
            },
            items: {
                hat: null,
                mount: null,
                leftWing: null,
                rightWing: null,
            },
            capturedAt: testDate,
            level: 2,
            trickeryPoints: 10,
            auraPoints: 5,
            gang: PigeonGang.LES_CRUMBS,
            captureId: "capture2",
        };

        // Add a pigeon for another trainer
        const pigeon3: PigeonDocument = {
            id: "pigeon3",
            ownerId: "anotherTrainerId",
            rarity: PigeonRarity.EPIC,
            baseStats: defaultStats,
            basePigeonId: "basePigeon3",
            currentFatigue: 5,
            maxFatigue: 5,
            originalPicture: {
                smallThumbnailUrl: "url3",
                originalUrl: "url3",
            },
            items: {
                hat: null,
                mount: null,
                leftWing: null,
                rightWing: null,
            },
            capturedAt: testDate,
            level: 3,
            trickeryPoints: 20,
            auraPoints: 15,
            gang: PigeonGang.MAISON_CONTI,
            captureId: "capture3",
        };

        await fakePigeonRepo.save(pigeon1);
        await fakePigeonRepo.save(pigeon2);
        await fakePigeonRepo.save(pigeon3);

        const result = await getTrainerPigeonsUseCase.execute(trainerId);

        // Should return only the pigeons owned by the trainer
        expect(result.pigeons.length).toBe(2);
        expect(result.pigeons.map((p) => p.id)).toContain("pigeon1");
        expect(result.pigeons.map((p) => p.id)).toContain("pigeon2");
        expect(result.pigeons.map((p) => p.id)).not.toContain("pigeon3");

        // Should include base pigeon data
        expect(result.pigeons[0]).toHaveProperty("basePigeon");
        expect(result.pigeons[1]).toHaveProperty("basePigeon");
        expect(result.pigeons[0].basePigeon.name).toBeDefined();
        expect(result.pigeons[1].basePigeon.name).toBeDefined();
    });

    it("should support pagination with limit and offset", async () => {
        const trainerId = "testTrainerId";
        const testDate = new Date();

        // Add 5 pigeons for the trainer
        for (let i = 1; i <= 5; i++) {
            const pigeon: PigeonDocument = {
                id: `pigeon${i}`,
                ownerId: trainerId,
                rarity: PigeonRarity.COMMON,
                baseStats: defaultStats,
                basePigeonId: "basePigeon1",
                currentFatigue: 5,
                maxFatigue: 5,
                originalPicture: {
                    smallThumbnailUrl: `url${i}`,
                    originalUrl: `url${i}`,
                },
                items: {
                    hat: null,
                    mount: null,
                    leftWing: null,
                    rightWing: null,
                },
                capturedAt: new Date(testDate.getTime() + i * 1000), // Different timestamps for sorting
                level: 1,
                trickeryPoints: 0,
                auraPoints: 0,
                gang: PigeonGang.MAISON_ROSSINI,
                captureId: `capture${i}`,
            };
            await fakePigeonRepo.save(pigeon);
        }

        // Test limit
        const limitResult = await getTrainerPigeonsUseCase.execute(trainerId, 2);
        expect(limitResult.pigeons.length).toBe(2);

        // Test offset
        const offsetResult = await getTrainerPigeonsUseCase.execute(trainerId, undefined, 2);
        expect(offsetResult.pigeons.length).toBe(3); // 5 total - 2 offset = 3

        // Test limit and offset together
        const limitOffsetResult = await getTrainerPigeonsUseCase.execute(trainerId, 2, 1);
        expect(limitOffsetResult.pigeons.length).toBe(2);

        // Should be sorted by capturedAt desc, so the first result should be the latest
        expect(limitResult.pigeons[0].id).toBe("pigeon5"); // Latest captured
    });
});

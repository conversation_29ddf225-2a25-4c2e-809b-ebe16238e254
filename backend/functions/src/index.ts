/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as dotenv from "dotenv";
import * as logger from "firebase-functions/logger";
import { HttpsError, onCall, onRequest } from "firebase-functions/v2/https";
import { onObjectFinalized } from "firebase-functions/v2/storage";
import path from "path";
dotenv.config({ path: path.resolve(__dirname, "../.env") });

import * as admin from "firebase-admin";
admin.initializeApp();

import { onSchedule } from "firebase-functions/scheduler";
import { TrainerDocument } from "./domain/entities/Trainer";
import { TrainerStatsOrderBy } from "./domain/enums/TrainerStatsOrderBy";
import { StatsService } from "./domain/services/StatsService";
import { FirestoreAnalysisJobRepository } from "./infrastructure/firebase/FirestoreAnalysisJobRepository";
import { FirestoreBasePigeonRepository } from "./infrastructure/firebase/FirestoreBasePigeonRepository";
import { FirestorePigeonRepository } from "./infrastructure/firebase/FirestorePigeonRepository";
import { FirestoreTrainerRepository } from "./infrastructure/firebase/FirestoreTrainerRepository";
import { FirestoreTrainerStatsRepository } from "./infrastructure/firebase/FirestoreTrainerStatsRepository";
import { AdvancedGeoLocationService } from "./infrastructure/geolocation/AdvancedGeoLocationService";
import { OpenAiServiceV2 } from "./infrastructure/openai/OpenAiServiceV2";
import { CapturePigeonUseCase } from "./use-cases/CapturePigeonUseCase";
import { CreateAnalysisJobUseCase } from "./use-cases/CreateAnalysisJobUseCase";
import { GetAnalysisJobStatusUseCase } from "./use-cases/GetAnalysisJobStatusUseCase";
import { GetLeaderBoardUseCase } from "./use-cases/GetLeaderBoardUseCase";
import { GetPigeonByCaptureIdUseCase } from "./use-cases/GetPigeonByCaptureIdUseCase";
import { GetTrainerPigeonsUseCase } from "./use-cases/GetTrainerPigeonsUseCase";
import { PingExternalServiceUseCase } from "./use-cases/PingExternalServiceUseCase";

const trainerRepo = new FirestoreTrainerRepository();
const pigeonRepo = new FirestorePigeonRepository();
const basePigeonRepo = new FirestoreBasePigeonRepository();
const analysisJobRepo = new FirestoreAnalysisJobRepository();
const trainerStatsRepo = new FirestoreTrainerStatsRepository();

const aiService = new OpenAiServiceV2(basePigeonRepo);
const geoLocationService = new AdvancedGeoLocationService();
const pingExternalServiceUseCase = new PingExternalServiceUseCase();
const statsService = new StatsService();

const capturePigeonUseCase = new CapturePigeonUseCase(
    trainerRepo,
    pigeonRepo,
    aiService,
    geoLocationService,
    analysisJobRepo,
    pingExternalServiceUseCase,
    statsService,
    trainerStatsRepo,
);

const createAnalysisJobUseCase = new CreateAnalysisJobUseCase(analysisJobRepo);

const getAnalysisJobStatusUseCase = new GetAnalysisJobStatusUseCase(analysisJobRepo);

const getTrainerPigeonsUseCase = new GetTrainerPigeonsUseCase(trainerRepo, pigeonRepo, basePigeonRepo);

const getPigeonByCaptureIdUseCase = new GetPigeonByCaptureIdUseCase(pigeonRepo, basePigeonRepo);

const getLeaderBoardUseCase = new GetLeaderBoardUseCase(trainerStatsRepo, trainerRepo);

export const onFileUpload = onObjectFinalized(
    {
        region: "us-east1",
        bucket: "pigeon-gogo.firebasestorage.app",
    },
    async (object) => {
        const { data } = object;
        const filePath = data.name;

        const signedFileUrl = (
            await admin
                .storage()
                .bucket("gs://pigeon-gogo.firebasestorage.app")
                .file(filePath)
                .getSignedUrl({ action: "read", expires: Date.now() + 1000 * 60 * 60 })
        )[0];

        if (filePath.startsWith("shots")) {
            // File path should be in format: shots/<trainerId>/<captureId>/<latitude>_<longitude>_<fileName>.jpg
            logger.info(`Processing file upload: ${filePath}`);

            try {
                const pathParts = filePath.split("/");
                if (pathParts.length < 3) {
                    logger.error("Invalid file path format");
                    return;
                }

                const trainerId = pathParts[1];
                const captureId = pathParts[2];

                // First, create the analysis job if it doesn't exist
                try {
                    await createAnalysisJobUseCase.execute({
                        captureId,
                        trainerId,
                        storageFilePath: filePath,
                    });
                    logger.info(`Created analysis job for captureId: ${captureId}`);
                } catch (error) {
                    logger.error("Error creating analysis job:", error); // Should not happen
                    return;
                }

                // Then process the capture
                capturePigeonUseCase.execute(filePath, signedFileUrl).catch((error: unknown) => {
                    logger.error("Error processing file upload:", error);
                    return;
                });
            } catch (error) {
                logger.error("Error in onFileUpload:", error);
            }
            return;
        }
        return;
    },
);

/**
 * HTTP function to get a trainer's pigeondex (all pigeons owned by the trainer)
 * Requires authentication - the user can only access their own pigeondex
 */
export const getTrainerPigeondex = onCall(
    {
        cors: true,
        region: "us-east1",
    },
    async (request) => {
        try {
            if (!request.auth) {
                throw new Error("Unauthorized - Authentication required");
            }

            const trainerId = request.auth.uid;
            const { limit, offset } = request.data ?? {};
            logger.info(`Getting pigeondex for trainer: ${trainerId}, limit: ${limit}, offset: ${offset}`);

            const pigeondex = await getTrainerPigeonsUseCase.execute(trainerId, limit, offset);
            return pigeondex;
        } catch (error: unknown) {
            logger.error("Error getting trainer pigeondex:", error);
            throw error;
        }
    },
);
/**
 * HTTP function to get a trainer's pigeondex (all pigeons owned by the trainer)
 * Requires authentication - the user can only access their own pigeondex
 */
export const getTrainerPigeondexRequest = onRequest(
    {
        cors: true,
        region: "us-east1",
    },
    async (req, res) => {
        try {
            const trainerId = req.body?.trainerId || req.query?.trainerId;
            const limit = req.body?.limit || req.query?.limit;
            const offset = req.body?.offset || req.query?.offset;

            logger.info(`Getting pigeondex for trainer: ${trainerId}, limit: ${limit}, offset: ${offset}`);

            const pigeondex = await getTrainerPigeonsUseCase.execute(trainerId, limit, offset);
            if (!pigeondex) {
                logger.info(`No deck not found for trainerId: ${trainerId}`);
                res.status(404).json({ error: "Deck not found" });
                return;
            }

            res.status(200).json(pigeondex);
        } catch (error: unknown) {
            logger.error("Error getting trainer pigeondex:", error);
            res.status(500).json({
                error: "Internal server error " + JSON.stringify(error),
            });
        }
    },
);

export const getPigeonByCaptureId = onCall(
    {
        cors: true,
        region: "us-east1",
    },
    async (request) => {
        try {
            if (!request.auth) {
                throw new Error("Unauthorized - Authentication required");
            }

            const trainerId = request.auth.uid;
            const { captureId } = request.data ?? {};

            if (!captureId) {
                throw new Error("Missing required parameter: captureId");
            }

            logger.info(`Getting pigeon by captureId: ${captureId} for trainer: ${trainerId}`);

            const pigeon = await getPigeonByCaptureIdUseCase.execute(captureId);

            if (!pigeon) {
                throw new HttpsError("not-found", "Pigeon not found");
            }

            return pigeon;
        } catch (error: unknown) {
            logger.error("Error getting pigeon by captureId:", error);
            throw error;
        }
    },
);

export const getPigeonByCaptureIdRequest = onRequest(
    {
        cors: true,
        region: "us-east1",
    },
    async (req, res) => {
        try {
            const captureId = req.body?.captureId || req.query?.captureId;

            if (!captureId) {
                logger.warn("Missing required parameter: captureId");
                res.status(400).json({ error: "Missing required parameter: captureId" });
                return;
            }

            logger.info(`Getting pigeon by captureId: ${captureId}`);

            const pigeon = await getPigeonByCaptureIdUseCase.execute(captureId);

            if (!pigeon) {
                logger.info(`Pigeon not found for captureId: ${captureId}`);
                res.status(404).json({ error: "Pigeon not found" });
                return;
            }

            res.status(200).json(pigeon);
        } catch (error: unknown) {
            logger.error("Error getting pigeon by captureId", error);
            res.status(500).json({
                error: error instanceof HttpsError ? error.message : "Internal server error",
            });
        }
    },
);

export const getCurrentLeaderBoardRequest = onRequest(
    {
        cors: true,
        region: "us-east1",
    },
    async (req, res) => {
        try {
            logger.info("Getting current leader board");
            const orderBy = req.query?.orderBy as TrainerStatsOrderBy;
            const leaderBoard = await getLeaderBoardUseCase.execute(orderBy, 100);
            res.status(200).json(leaderBoard);
        } catch (error: unknown) {
            logger.error("Error getting leader board:", error);
            res.status(500).json({
                error: "Internal server error",
            });
        }
    },
);

export const updateTrainerUsernameRequest = onRequest(
    {
        cors: true,
        region: "us-east1",
    },
    async (req, res) => {
        try {
            const trainerId = req.body?.trainerId || req.query?.trainerId;
            const username = req.body?.username || req.query?.username;

            if (!trainerId || !username) {
                logger.warn("Missing required parameter: trainerId or username");
                res.status(400).json({ error: "Missing required parameter: trainerId or username" });
                return;
            }

            logger.info(`Updating trainer username: ${trainerId} to ${username}`);
            await trainerRepo.update({ id: trainerId, username } as TrainerDocument);
            res.status(200).json({ message: "Username updated" });
        } catch (error: unknown) {
            logger.error("Error updating trainer username:", error);
            res.status(500).json({
                error: "Internal server error",
            });
        }
    },
);

// cron job that will run every hour and will increase pigeonballs stack by 1 for every trainer
export const refillPigeonBalls = onSchedule("0 * * * *", async () => {
    try {
        logger.info("Refilling pigeon balls");
        const trainerCollection = admin.firestore().collection("trainers");
        const allTrainers = await trainerCollection.where("pigeonBalls", "<", 4).get();
        for (const trainerDoc of allTrainers.docs) {
            await trainerCollection.doc(trainerDoc.id).update({
                pigeonBalls: admin.firestore.FieldValue.increment(1),
            });
        }
    } catch (error: unknown) {
        logger.error("Error refilling pigeon balls:", error);
    }
});

export const getBasePigeonStatsRequest = onRequest(
    {
        cors: true,
        region: "us-east1",
    },
    async (req, res) => {
        try {
            logger.info("Getting base pigeon stats");
            const basePigeonStats = await basePigeonStatsRepo.getStats();
            res.status(200).json(basePigeonStats);
        } catch (error: unknown) {
            logger.error("Error getting base pigeon stats:", error);
            res.status(500).json({
                error: "Internal server error",
            });
        }
    },
);

/**
 * HTTP function to get the status of an analysis job
 * Requires authentication - the user can only access their own jobs
 */
export const getAnalysisJobStatus = onCall(
    {
        cors: true,
        region: "us-east1",
    },
    async (request) => {
        try {
            if (!request.auth) {
                throw new Error("Unauthorized - Authentication required");
            }

            const trainerId = request.auth.uid;
            const { captureId } = request.data ?? {};

            if (!captureId) {
                throw new Error("Missing required parameter: captureId");
            }

            logger.info(`Getting analysis job status for trainer: ${trainerId}, captureId: ${captureId}`);

            const analysisJob = await getAnalysisJobStatusUseCase.execute(captureId);

            if (!analysisJob) {
                throw new HttpsError("not-found", "Analysis job not found");
            }

            // Verify that the job belongs to the authenticated user
            if (analysisJob.trainerId !== trainerId) {
                throw new Error("Unauthorized - You can only access your own analysis jobs");
            }

            return analysisJob;
        } catch (error: unknown) {
            logger.error("Error getting analysis job status:", error);
            throw error;
        }
    },
);

export const getAnalysisJobStatusRequest = onRequest(
    {
        cors: true,
        region: "us-east1",
    },
    async (req, res) => {
        if (!req.query.captureId) {
            res.status(400).json({ error: "Missing required parameter: captureId" });
            return;
        }
        const captureId = req.query.captureId as string;
        const analysisJob = await getAnalysisJobStatusUseCase.execute(captureId);
        if (!analysisJob) {
            res.status(404).json({ error: "Analysis job not found" });
            return;
        }
        res.status(200).json(analysisJob);
    },
);
